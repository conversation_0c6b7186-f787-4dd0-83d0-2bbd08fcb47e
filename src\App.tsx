import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { BlogProvider } from './contexts/BlogContext';
import Layout from './components/Layout';
import HomePage from './pages/HomePage';
import BlogListPage from './pages/BlogListPage';
import BlogPage from './pages/BlogPage';
import CategoryPage from './pages/CategoryPage';
import AdminLayout from './pages/admin/AdminLayout';
import AdminDashboard from './pages/admin/AdminDashboard';
import PostManager from './pages/admin/PostManager';

function App() {
  return (
    <BlogProvider>
      <Router>
        <Routes>
          {/* Public Routes */}
          <Route path="/" element={<Layout><HomePage /></Layout>} />
          <Route path="/blog" element={<Layout><BlogListPage /></Layout>} />
          <Route path="/blog/:slug" element={<Layout><BlogPage /></Layout>} />
          <Route path="/category/:category" element={<Layout><CategoryPage /></Layout>} />
          
          {/* Admin Routes */}
          <Route path="/admin" element={<AdminLayout />}>
            <Route index element={<AdminDashboard />} />
            <Route path="posts" element={<PostManager />} />
            <Route path="categories" element={<div className="p-6"><h1 className="text-2xl font-bold">Categories Management</h1><p className="text-gray-600 mt-2">Category management interface coming soon...</p></div>} />
            <Route path="analytics" element={<div className="p-6"><h1 className="text-2xl font-bold">Advanced Analytics</h1><p className="text-gray-600 mt-2">Detailed analytics interface coming soon...</p></div>} />
            <Route path="users" element={<div className="p-6"><h1 className="text-2xl font-bold">User Management</h1><p className="text-gray-600 mt-2">User management interface coming soon...</p></div>} />
            <Route path="settings" element={<div className="p-6"><h1 className="text-2xl font-bold">Settings</h1><p className="text-gray-600 mt-2">Settings interface coming soon...</p></div>} />
          </Route>
        </Routes>
      </Router>
    </BlogProvider>
  );
}

export default App;