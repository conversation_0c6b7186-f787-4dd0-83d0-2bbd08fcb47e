import React from 'react';
import { Link } from 'react-router-dom';
import { <PERSON>R<PERSON>, Star, Shield, Zap, Users, TrendingUp, Award } from 'lucide-react';
import { useBlog } from '../contexts/BlogContext';
import BlogGrid from '../components/Blog/BlogGrid';

const HomePage: React.FC = () => {
  const { posts, categories } = useBlog();
  const featuredPosts = posts.filter(post => post.featured).slice(0, 3);
  const latestPosts = posts.slice(0, 6);

  const features = [
    {
      icon: Star,
      title: 'Expert Reviews',
      description: 'In-depth reviews from experienced riders and industry experts'
    },
    {
      icon: Shield,
      title: 'Safety First',
      description: 'Comprehensive safety guides and regulatory information'
    },
    {
      icon: Zap,
      title: 'Performance Focus',
      description: 'Real-world testing of speed, range, and durability'
    },
    {
      icon: Users,
      title: 'Community Driven',
      description: 'Reviews and insights from our active community of riders'
    }
  ];

  const stats = [
    { label: 'Scooters Reviewed', value: '150+', icon: Award },
    { label: 'Expert Contributors', value: '25+', icon: Users },
    { label: 'Monthly Readers', value: '50K+', icon: TrendingUp },
    { label: 'Years Experience', value: '5+', icon: Star }
  ];

  return (
    <>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 text-white">
        <div className="absolute inset-0 bg-black opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl lg:text-6xl font-bold leading-tight mb-6">
                Your Guide to
                <span className="text-blue-300 block">Electric Scooters</span>
              </h1>
              <p className="text-xl text-blue-100 mb-8 leading-relaxed">
                Discover the best electric scooters through expert reviews, comprehensive buying guides, 
                and real-world testing. Make informed decisions for your urban mobility needs.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                <Link
                  to="/category/reviews"
                  className="bg-blue-600 hover:bg-blue-700 px-8 py-4 rounded-lg font-semibold text-lg transition-colors flex items-center justify-center space-x-2"
                >
                  <span>Browse Reviews</span>
                  <ArrowRight className="w-5 h-5" />
                </Link>
                <Link
                  to="/blog/top-10-electric-scooters-2024-buyers-guide"
                  className="border-2 border-blue-300 hover:bg-blue-300 hover:text-blue-900 px-8 py-4 rounded-lg font-semibold text-lg transition-colors text-center"
                >
                  Buying Guide 2024
                </Link>
              </div>
            </div>
            <div className="relative">
              <img
                src="https://images.pexels.com/photos/3799821/pexels-photo-3799821.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="Electric Scooter"
                className="rounded-2xl shadow-2xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white text-gray-900 p-6 rounded-xl shadow-lg">
                <div className="flex items-center space-x-3">
                  <div className="bg-green-100 p-3 rounded-full">
                    <Star className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <p className="font-bold text-lg">4.8/5 Rating</p>
                    <p className="text-gray-600">Trusted Reviews</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => {
              const Icon = stat.icon;
              return (
                <div key={index} className="text-center">
                  <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <div className="text-3xl font-bold text-gray-900 mb-2">{stat.value}</div>
                  <div className="text-gray-600">{stat.label}</div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Why Choose Scooter AS?</h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              We're dedicated to providing you with the most accurate, comprehensive, and helpful 
              electric scooter information available.
            </p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const Icon = feature.icon;
              return (
                <div key={index} className="text-center p-6 rounded-xl hover:shadow-lg transition-shadow">
                  <div className="bg-blue-100 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">{feature.title}</h3>
                  <p className="text-gray-600 leading-relaxed">{feature.description}</p>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Featured Posts */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">Featured Reviews</h2>
            <p className="text-xl text-gray-600">
              Our most popular and comprehensive electric scooter reviews
            </p>
          </div>
          <BlogGrid posts={featuredPosts} />
          <div className="text-center mt-8">
            <Link
              to="/category/reviews"
              className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center space-x-2"
            >
              <span>View All Reviews</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Latest Posts */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <BlogGrid posts={latestPosts} title="Latest Posts" />
          <div className="text-center mt-8">
            <Link
              to="/blog"
              className="bg-gray-900 hover:bg-gray-800 text-white px-8 py-3 rounded-lg font-semibold transition-colors inline-flex items-center space-x-2"
            >
              <span>View All Posts</span>
              <ArrowRight className="w-5 h-5" />
            </Link>
          </div>
        </div>
      </section>

      {/* Newsletter CTA */}
      <section className="py-16 bg-blue-600">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            Stay Updated with Latest Reviews
          </h2>
          <p className="text-xl text-blue-100 mb-8">
            Get weekly updates on new scooter reviews, buying guides, and industry news
          </p>
          <div className="flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0 sm:space-x-4 max-w-md mx-auto">
            <input
              type="email"
              placeholder="Enter your email"
              className="w-full px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-300"
            />
            <button className="w-full sm:w-auto bg-yellow-500 hover:bg-yellow-600 text-black px-6 py-3 rounded-lg font-semibold transition-colors">
              Subscribe
            </button>
          </div>
          <p className="text-blue-200 text-sm mt-4">
            No spam, unsubscribe at any time
          </p>
        </div>
      </section>
    </>
  );
};

export default HomePage;