import React from 'react';
import BlogCard from './BlogCard';
import { BlogPost } from '../../types';

interface BlogGridProps {
  posts: BlogPost[];
  title?: string;
  showFeatured?: boolean;
}

const BlogGrid: React.FC<BlogGridProps> = ({ posts, title, showFeatured = false }) => {
  const featuredPosts = posts.filter(post => post.featured);
  const regularPosts = posts.filter(post => !post.featured);

  return (
    <section className="py-8">
      {title && (
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">{title}</h2>
          <div className="w-24 h-1 bg-blue-600 rounded"></div>
        </div>
      )}

      {/* Featured Posts */}
      {showFeatured && featuredPosts.length > 0 && (
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6">Featured Posts</h3>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {featuredPosts.slice(0, 2).map((post) => (
              <BlogCard key={post.id} post={post} featured={true} />
            ))}
          </div>
        </div>
      )}

      {/* Regular Posts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {regularPosts.map((post) => (
          <BlogCard key={post.id} post={post} />
        ))}
      </div>

      {posts.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-500 text-lg">No posts found.</p>
        </div>
      )}
    </section>
  );
};

export default BlogGrid;