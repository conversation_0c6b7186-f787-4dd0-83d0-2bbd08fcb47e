import React from 'react';
import { <PERSON><PERSON><PERSON>, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LineChart, Line, PieChart, Pie, Cell } from 'recharts';
import { TrendingUp, Users, Eye, Clock, FileText, MessageSquare, Star, DollarSign } from 'lucide-react';
import { useBlog } from '../../contexts/BlogContext';
import { format } from 'date-fns';

const AdminDashboard: React.FC = () => {
  const { analytics, posts, categories } = useBlog();

  const stats = [
    {
      name: 'Page Views',
      value: analytics.pageViews.toLocaleString(),
      change: '+12.5%',
      changeType: 'positive',
      icon: Eye
    },
    {
      name: 'Unique Visitors',
      value: analytics.uniqueVisitors.toLocaleString(),
      change: '****%',
      changeType: 'positive',
      icon: Users
    },
    {
      name: 'Avg. Session',
      value: `${Math.floor(analytics.averageSessionDuration / 60)}m ${analytics.averageSessionDuration % 60}s`,
      change: '****%',
      changeType: 'positive',
      icon: Clock
    },
    {
      name: 'Bounce Rate',
      value: `${Math.round(analytics.bounceRate * 100)}%`,
      change: '-2.3%',
      changeType: 'positive',
      icon: TrendingUp
    },
    {
      name: 'Total Posts',
      value: posts.length.toString(),
      change: '+3',
      changeType: 'positive',
      icon: FileText
    },
    {
      name: 'Featured Posts',
      value: posts.filter(p => p.featured).length.toString(),
      change: '+1',
      changeType: 'positive',
      icon: Star
    },
    {
      name: 'Categories',
      value: categories.length.toString(),
      change: '0',
      changeType: 'neutral',
      icon: MessageSquare
    },
    {
      name: 'Est. Revenue',
      value: '$2,450',
      change: '+15.3%',
      changeType: 'positive',
      icon: DollarSign
    }
  ];

  const monthlyData = [
    { name: 'Jan', views: 4000, visitors: 2400 },
    { name: 'Feb', views: 3000, visitors: 1398 },
    { name: 'Mar', views: 2000, visitors: 9800 },
    { name: 'Apr', views: 2780, visitors: 3908 },
    { name: 'May', views: 1890, visitors: 4800 },
    { name: 'Jun', views: 2390, visitors: 3800 },
    { name: 'Jul', views: 3490, visitors: 4300 }
  ];

  const categoryData = categories.map(cat => ({
    name: cat.name,
    value: cat.postCount,
    color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'][Math.floor(Math.random() * 5)]
  }));

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  return (
    <div className="p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Dashboard</h1>
        <p className="text-gray-600">Welcome back! Here's what's happening with your blog.</p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {stats.map((stat, index) => {
          const Icon = stat.icon;
          return (
            <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.name}</p>
                  <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                </div>
                <div className="bg-blue-50 p-3 rounded-full">
                  <Icon className="w-6 h-6 text-blue-600" />
                </div>
              </div>
              <div className="mt-4 flex items-center">
                <span className={`text-sm font-medium ${
                  stat.changeType === 'positive' ? 'text-green-600' : 
                  stat.changeType === 'negative' ? 'text-red-600' : 'text-gray-600'
                }`}>
                  {stat.change}
                </span>
                <span className="text-sm text-gray-500 ml-2">vs last month</span>
              </div>
            </div>
          );
        })}
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        {/* Traffic Chart */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Traffic Overview</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={monthlyData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Line type="monotone" dataKey="views" stroke="#3B82F6" strokeWidth={2} />
              <Line type="monotone" dataKey="visitors" stroke="#10B981" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </div>

        {/* Category Distribution */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Posts by Category</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={categoryData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {categoryData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Tables Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Pages */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Pages</h3>
          <div className="space-y-4">
            {analytics.topPages.map((page, index) => (
              <div key={index} className="flex items-center justify-between py-2">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900 truncate">{page.page}</p>
                </div>
                <div className="ml-4">
                  <span className="text-sm font-semibold text-gray-600">
                    {page.views.toLocaleString()} views
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Traffic Sources */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Traffic Sources</h3>
          <div className="space-y-4">
            {analytics.trafficSources.map((source, index) => (
              <div key={index} className="flex items-center justify-between py-2">
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-900">{source.source}</p>
                </div>
                <div className="ml-4">
                  <span className="text-sm font-semibold text-gray-600">
                    {source.visitors.toLocaleString()} visitors
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="mt-8 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h3>
        <div className="space-y-3">
          {analytics.recentActivity.map((activity, index) => (
            <div key={index} className="flex items-center space-x-3 py-2">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <div className="flex-1">
                <p className="text-sm text-gray-900">
                  Visitor from <span className="font-medium">{activity.visitor}</span> viewed{' '}
                  <span className="font-medium">{activity.page}</span>
                </p>
                <p className="text-xs text-gray-500">{activity.timestamp}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;