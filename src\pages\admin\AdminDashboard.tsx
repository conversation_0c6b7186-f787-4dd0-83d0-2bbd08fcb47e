import React, { useState } from 'react';
import {
  Users,
  FileText,
  Eye,
  TrendingUp,
  Calendar,
  MessageSquare,
  Star,
  Clock,
  BarChart3,
  PieChart,
  Activity,
  ArrowUpRight,
  ArrowDownRight,
  Plus,
  Settings,
  Bell,
  Search
} from 'lucide-react';

const AdminDashboard: React.FC = () => {
  const [selectedPeriod, setSelectedPeriod] = useState('7d');

  const stats = [
    {
      title: 'ASSIGNED TICKETS',
      value: '30',
      change: '+12%',
      changeType: 'positive' as const,
      icon: FileText,
      color: 'blue',
      bgColor: 'bg-blue-500'
    },
    {
      title: 'ACTIVE TICKETS',
      value: '25',
      change: '+8%',
      changeType: 'positive' as const,
      icon: Clock,
      color: 'orange',
      bgColor: 'bg-orange-500'
    },
    {
      title: 'ACTIVE FAQ TICKET',
      value: '15',
      change: '+15%',
      changeType: 'positive' as const,
      icon: MessageSquare,
      color: 'purple',
      bgColor: 'bg-purple-500'
    },
    {
      title: 'TOTAL CLOSED TICKETS',
      value: '10',
      change: '-3%',
      changeType: 'negative' as const,
      icon: Star,
      color: 'green',
      bgColor: 'bg-green-500'
    }
  ];

  const chartData = [
    { month: 'Feb', assigned: 85, closed: 40, pending: 35 },
    { month: 'Mar', assigned: 90, closed: 45, pending: 40 },
    { month: 'Apr', assigned: 110, closed: 60, pending: 50 },
    { month: 'May', assigned: 95, closed: 55, pending: 45 },
    { month: 'Jun', assigned: 120, closed: 70, pending: 55 },
    { month: 'Jul', assigned: 105, closed: 65, pending: 50 },
    { month: 'Aug', assigned: 130, closed: 75, pending: 60 },
    { month: 'Sep', assigned: 140, closed: 80, pending: 65 },
    { month: 'Oct', assigned: 125, closed: 85, pending: 70 }
  ];

  const recentArticles = [
    {
      id: 1,
      title: 'Best electric scooter news difficulties',
      status: 'published',
      count: 81
    },
    {
      id: 2,
      title: 'Phasellus non lorem',
      status: 'draft',
      count: 50
    },
    {
      id: 3,
      title: 'Phasellus vitae sapien pretium',
      status: 'published',
      count: 20
    },
    {
      id: 4,
      title: 'Suspendisse eget nec non',
      status: 'published',
      count: 10
    },
    {
      id: 5,
      title: 'Morbi tempus sagittis a nisi porta',
      status: 'draft',
      count: 20
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900">Dashboard</h1>
            <p className="text-sm text-gray-500">Control panel</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>Home</span>
              <span>/</span>
              <span className="text-gray-900">Dashboard</span>
            </div>
          </div>
        </div>
      </div>

      <div className="p-6 space-y-6">
        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon;
            return (
              <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 relative overflow-hidden">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className={`w-12 h-12 ${stat.bgColor} rounded-full flex items-center justify-center mb-4`}>
                      <Icon className="w-6 h-6 text-white" />
                    </div>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className="text-xs font-medium text-gray-500 uppercase tracking-wide mt-1">
                      {stat.title}
                    </p>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Visitor Analytics Chart */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">Visitor Analytics</h2>
              <div className="flex items-center space-x-2">
                <button
                  className={`px-3 py-1 text-sm rounded-lg ${selectedPeriod === '7d' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
                  onClick={() => setSelectedPeriod('7d')}
                >
                  7 Days
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded-lg ${selectedPeriod === '30d' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
                  onClick={() => setSelectedPeriod('30d')}
                >
                  30 Days
                </button>
                <button
                  className={`px-3 py-1 text-sm rounded-lg ${selectedPeriod === '90d' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
                  onClick={() => setSelectedPeriod('90d')}
                >
                  90 Days
                </button>
              </div>
            </div>

            {/* Chart Area */}
            <div className="h-80 relative">
              <div className="absolute inset-0 flex items-end justify-between px-4 pb-8">
                {chartData.map((data, index) => (
                  <div key={index} className="flex flex-col items-center space-y-2 flex-1">
                    <div className="flex flex-col items-center space-y-1 w-full max-w-12">
                      {/* Visitors Bar */}
                      <div
                        className="w-6 bg-gradient-to-t from-blue-500 to-blue-400 rounded-t hover:from-blue-600 hover:to-blue-500 transition-colors cursor-pointer"
                        style={{ height: `${(data.assigned / 140) * 200}px` }}
                        title={`${data.month}: ${data.assigned} visitors`}
                      ></div>
                    </div>
                    <span className="text-xs text-gray-500 mt-2">{data.month}</span>
                  </div>
                ))}
              </div>

              {/* Y-axis labels */}
              <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 py-8">
                <span>140</span>
                <span>120</span>
                <span>100</span>
                <span>80</span>
                <span>60</span>
                <span>40</span>
                <span>20</span>
                <span>0</span>
              </div>
            </div>

            {/* Legend */}
            <div className="flex items-center justify-center mt-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Daily Visitors</span>
              </div>
            </div>
          </div>

          {/* Admin Profile Card */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="text-center">
              <div className="w-20 h-20 bg-gradient-to-r from-green-400 to-blue-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl font-bold text-white">A</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">admin</h3>
              <p className="text-sm text-gray-500 mb-6">Super Admin</p>

              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-500">Email Address:</span>
                  <span className="text-gray-900"><EMAIL></span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Contact Number:</span>
                  <span className="text-gray-900">+88-1234-56789</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Date of Birth:</span>
                  <span className="text-gray-900">Nov 20, 2000</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Join Date:</span>
                  <span className="text-gray-900">Feb 10, 2018</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-500">Timezone:</span>
                  <span className="text-gray-900">USA</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <a
              href="/"
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-blue-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors group"
            >
              <Eye className="w-5 h-5 text-blue-500 group-hover:text-blue-600" />
              <span className="text-blue-600 group-hover:text-blue-700 font-medium">View Frontend</span>
            </a>
            <button className="flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-green-300 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors group">
              <Plus className="w-5 h-5 text-green-500 group-hover:text-green-600" />
              <span className="text-green-600 group-hover:text-green-700 font-medium">Create Post</span>
            </button>
            <button className="flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-purple-300 rounded-lg hover:border-purple-500 hover:bg-purple-50 transition-colors group">
              <Users className="w-5 h-5 text-purple-500 group-hover:text-purple-600" />
              <span className="text-purple-600 group-hover:text-purple-700 font-medium">Manage Users</span>
            </button>
            <button className="flex items-center justify-center space-x-2 p-4 border-2 border-dashed border-orange-300 rounded-lg hover:border-orange-500 hover:bg-orange-50 transition-colors group">
              <Settings className="w-5 h-5 text-orange-500 group-hover:text-orange-600" />
              <span className="text-orange-600 group-hover:text-orange-700 font-medium">Settings</span>
            </button>
          </div>
        </div>

        {/* Recent Posts & Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Recent Posts */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Recent Posts</h3>
              <button className="text-sm text-blue-600 hover:text-blue-800 font-medium">View All</button>
            </div>
            <div className="space-y-3">
              {recentArticles.map((article) => (
                <div key={article.id} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 rounded-lg px-2 transition-colors">
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${article.status === 'published' ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                    <div>
                      <span className="text-sm font-medium text-gray-900 block">{article.title}</span>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        article.status === 'published'
                          ? 'bg-green-100 text-green-700'
                          : 'bg-yellow-100 text-yellow-700'
                      }`}>
                        {article.status}
                      </span>
                    </div>
                  </div>
                  <div className="text-right">
                    <span className="text-sm font-medium text-gray-600">{article.count}</span>
                    <span className="text-xs text-gray-500 block">views</span>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* System Status */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">System Status</h3>
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-green-600 font-medium">All Systems Operational</span>
              </div>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                    <Activity className="w-4 h-4 text-green-600" />
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-900">Server Status</span>
                    <span className="text-xs text-gray-500 block">Last checked: 2 min ago</span>
                  </div>
                </div>
                <span className="text-sm font-medium text-green-600">Online</span>
              </div>

              <div className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Eye className="w-4 h-4 text-blue-600" />
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-900">Database</span>
                    <span className="text-xs text-gray-500 block">Response time: 45ms</span>
                  </div>
                </div>
                <span className="text-sm font-medium text-green-600">Healthy</span>
              </div>

              <div className="flex items-center justify-between py-2">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                    <TrendingUp className="w-4 h-4 text-purple-600" />
                  </div>
                  <div>
                    <span className="text-sm font-medium text-gray-900">CDN Status</span>
                    <span className="text-xs text-gray-500 block">Global coverage: 99.9%</span>
                  </div>
                </div>
                <span className="text-sm font-medium text-green-600">Active</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminDashboard;