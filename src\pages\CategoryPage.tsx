import React from 'react';
import { useParams } from 'react-router-dom';
import { useBlog } from '../contexts/BlogContext';
import BlogGrid from '../components/Blog/BlogGrid';

const CategoryPage: React.FC = () => {
  const { category } = useParams<{ category: string }>();
  const { posts, categories } = useBlog();

  const categoryInfo = categories.find(cat => 
    cat.slug === category || cat.name.toLowerCase().replace(' ', '-') === category
  );

  const categoryPosts = posts.filter(post => 
    post.category.toLowerCase().replace(' ', '-') === category ||
    post.category === categoryInfo?.name
  );

  if (!categoryInfo && categoryPosts.length === 0) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Category Not Found</h1>
        <p className="text-xl text-gray-600">The category you're looking for doesn't exist.</p>
      </div>
    );
  }

  const displayName = categoryInfo?.name || category?.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Category';
  const description = categoryInfo?.description || `Posts in the ${displayName} category`;

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="mb-8">
        <nav className="mb-4">
          <ol className="flex items-center space-x-2 text-sm text-gray-500">
            <li><a href="/" className="hover:text-blue-600">Home</a></li>
            <li>/</li>
            <li><a href="/blog" className="hover:text-blue-600">Blog</a></li>
            <li>/</li>
            <li className="text-gray-900">{displayName}</li>
          </ol>
        </nav>

        <h1 className="text-4xl font-bold text-gray-900 mb-4">{displayName}</h1>
        <p className="text-xl text-gray-600 mb-4">{description}</p>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <p className="text-blue-800">
            <strong>{categoryPosts.length}</strong> posts in this category
          </p>
        </div>
      </div>

      {/* Posts */}
      <BlogGrid posts={categoryPosts} showFeatured={true} />
    </div>
  );
};

export default CategoryPage;