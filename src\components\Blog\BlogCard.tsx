import React from 'react';
import { Link } from 'react-router-dom';
import { Calendar, Clock, User, Tag } from 'lucide-react';
import { BlogPost } from '../../types';
import { format } from 'date-fns';

interface BlogCardProps {
  post: BlogPost;
  featured?: boolean;
}

const BlogCard: React.FC<BlogCardProps> = ({ post, featured = false }) => {
  const cardClasses = featured
    ? 'bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1'
    : 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300';

  return (
    <article className={cardClasses}>
      <Link to={`/blog/${post.slug}`}>
        <div className="relative overflow-hidden">
          <img
            src={post.coverImage}
            alt={post.title}
            className={`w-full object-cover transition-transform duration-300 hover:scale-105 ${
              featured ? 'h-64' : 'h-48'
            }`}
          />
          <div className="absolute top-4 left-4">
            <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
              {post.category}
            </span>
          </div>
          {featured && (
            <div className="absolute top-4 right-4">
              <span className="bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold">
                Featured
              </span>
            </div>
          )}
        </div>
      </Link>

      <div className={`p-6 ${featured ? 'p-8' : ''}`}>
        <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4" />
            <span>{format(new Date(post.publishedAt), 'MMM dd, yyyy')}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>{post.readTime} min read</span>
          </div>
          <div className="flex items-center space-x-1">
            <User className="w-4 h-4" />
            <span>{post.author}</span>
          </div>
        </div>

        <Link to={`/blog/${post.slug}`}>
          <h2 className={`font-bold text-gray-900 mb-3 hover:text-blue-600 transition-colors ${
            featured ? 'text-2xl leading-tight' : 'text-xl'
          }`}>
            {post.title}
          </h2>
        </Link>

        <p className={`text-gray-600 mb-4 leading-relaxed ${
          featured ? 'text-lg' : ''
        }`}>
          {post.excerpt}
        </p>

        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Tag className="w-4 h-4 text-gray-400" />
            <div className="flex flex-wrap gap-2">
              {post.tags.slice(0, 3).map((tag) => (
                <span
                  key={tag}
                  className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded-full"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>

          <Link
            to={`/blog/${post.slug}`}
            className="text-blue-600 hover:text-blue-700 font-medium text-sm transition-colors"
          >
            Read More →
          </Link>
        </div>

        {post.affiliateLinks.length > 0 && (
          <div className="mt-4 pt-4 border-t border-gray-100">
            <div className="flex flex-wrap gap-2">
              {post.affiliateLinks.slice(0, 2).map((link) => (
                <a
                  key={link.id}
                  href={link.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center space-x-2"
                >
                  <span>{link.text}</span>
                  {link.discount && (
                    <span className="bg-yellow-400 text-black px-2 py-1 rounded text-xs">
                      {link.discount}
                    </span>
                  )}
                </a>
              ))}
            </div>
          </div>
        )}
      </div>
    </article>
  );
};

export default BlogCard;