import React, { useState } from 'react';
import { use<PERSON><PERSON><PERSON>, <PERSON> } from 'react-router-dom';
import { Calendar, Clock, User, Tag, ArrowLeft, Share2, BookOpen, ExternalLink } from 'lucide-react';
import ReactMarkdown from 'react-markdown';
import { Prism as Syntax<PERSON>ighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';
import { useBlog } from '../contexts/BlogContext';
import { format } from 'date-fns';

const BlogPage: React.FC = () => {
  const { slug } = useParams<{ slug: string }>();
  const { getPostBySlug, posts } = useBlog();
  const [showShareMenu, setShowShareMenu] = useState(false);

  const post = slug ? getPostBySlug(slug) : null;

  if (!post) {
    return (
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-16 text-center">
        <h1 className="text-4xl font-bold text-gray-900 mb-4">Post Not Found</h1>
        <p className="text-xl text-gray-600 mb-8">The blog post you're looking for doesn't exist.</p>
        <Link
          to="/blog"
          className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-flex items-center space-x-2"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Blog</span>
        </Link>
      </div>
    );
  }

  const relatedPosts = posts
    .filter(p => p.id !== post.id && p.category === post.category)
    .slice(0, 3);

  const shareUrl = `${window.location.origin}/blog/${post.slug}`;
  const shareTitle = post.title;

  const handleShare = (platform: string) => {
    const urls = {
      twitter: `https://twitter.com/intent/tweet?text=${encodeURIComponent(shareTitle)}&url=${encodeURIComponent(shareUrl)}`,
      facebook: `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareUrl)}`,
      linkedin: `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(shareUrl)}`
    };
    
    window.open(urls[platform as keyof typeof urls], '_blank', 'width=600,height=400');
    setShowShareMenu(false);
  };

  return (
    <article className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Breadcrumb */}
      <nav className="mb-8">
        <ol className="flex items-center space-x-2 text-sm text-gray-500">
          <li><Link to="/" className="hover:text-blue-600">Home</Link></li>
          <li>/</li>
          <li><Link to="/blog" className="hover:text-blue-600">Blog</Link></li>
          <li>/</li>
          <li><Link to={`/category/${post.category.toLowerCase().replace(' ', '-')}`} className="hover:text-blue-600">{post.category}</Link></li>
          <li>/</li>
          <li className="text-gray-900">{post.title}</li>
        </ol>
      </nav>

      {/* Header */}
      <header className="mb-8">
        <div className="flex items-center space-x-2 mb-4">
          <span className="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
            {post.category}
          </span>
          {post.featured && (
            <span className="bg-yellow-500 text-black px-3 py-1 rounded-full text-sm font-bold">
              Featured
            </span>
          )}
        </div>

        <h1 className="text-4xl lg:text-5xl font-bold text-gray-900 mb-6 leading-tight">
          {post.title}
        </h1>

        <p className="text-xl text-gray-600 mb-6 leading-relaxed">
          {post.excerpt}
        </p>

        {/* Meta Information */}
        <div className="flex flex-wrap items-center space-x-6 text-sm text-gray-500 mb-6">
          <div className="flex items-center space-x-1">
            <User className="w-4 h-4" />
            <span>{post.author}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Calendar className="w-4 h-4" />
            <span>{format(new Date(post.publishedAt), 'MMMM dd, yyyy')}</span>
          </div>
          <div className="flex items-center space-x-1">
            <Clock className="w-4 h-4" />
            <span>{post.readTime} min read</span>
          </div>
          <div className="flex items-center space-x-1">
            <BookOpen className="w-4 h-4" />
            <span>{post.content.split(' ').length} words</span>
          </div>
        </div>

        {/* Share Button */}
        <div className="relative">
          <button
            onClick={() => setShowShareMenu(!showShareMenu)}
            className="flex items-center space-x-2 bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-lg transition-colors"
          >
            <Share2 className="w-4 h-4" />
            <span>Share</span>
          </button>
          
          {showShareMenu && (
            <div className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
              <button
                onClick={() => handleShare('twitter')}
                className="block w-full text-left px-4 py-2 hover:bg-gray-50 text-sm"
              >
                Share on Twitter
              </button>
              <button
                onClick={() => handleShare('facebook')}
                className="block w-full text-left px-4 py-2 hover:bg-gray-50 text-sm"
              >
                Share on Facebook
              </button>
              <button
                onClick={() => handleShare('linkedin')}
                className="block w-full text-left px-4 py-2 hover:bg-gray-50 text-sm"
              >
                Share on LinkedIn
              </button>
            </div>
          )}
        </div>
      </header>

      {/* Cover Image */}
      <div className="mb-8">
        <img
          src={post.coverImage}
          alt={post.title}
          className="w-full h-96 object-cover rounded-xl shadow-lg"
        />
      </div>

      {/* Content */}
      <div className="prose prose-lg max-w-none mb-8">
        <ReactMarkdown
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                <SyntaxHighlighter
                  style={tomorrow}
                  language={match[1]}
                  PreTag="div"
                  {...props}
                >
                  {String(children).replace(/\n$/, '')}
                </SyntaxHighlighter>
              ) : (
                <code className={className} {...props}>
                  {children}
                </code>
              );
            }
          }}
        >
          {post.content}
        </ReactMarkdown>
      </div>

      {/* Affiliate Links */}
      {post.affiliateLinks.length > 0 && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-green-800 mb-4">🛒 Where to Buy</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {post.affiliateLinks.map((link) => (
              <a
                key={link.id}
                href={link.url}
                target="_blank"
                rel="noopener noreferrer"
                className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg transition-colors flex items-center justify-between"
              >
                <div>
                  <div className="font-semibold">{link.productName}</div>
                  <div className="text-sm opacity-90">{link.text}</div>
                  {link.price && (
                    <div className="text-sm font-bold mt-1">{link.price}</div>
                  )}
                </div>
                <div className="text-right">
                  {link.discount && (
                    <div className="bg-yellow-400 text-black px-2 py-1 rounded text-xs font-bold mb-1">
                      {link.discount}
                    </div>
                  )}
                  <ExternalLink className="w-5 h-5" />
                </div>
              </a>
            ))}
          </div>
          <p className="text-xs text-green-700 mt-4">
            * As an Amazon Associate, we earn from qualifying purchases at no additional cost to you.
          </p>
        </div>
      )}

      {/* Tags */}
      <div className="flex items-center space-x-4 mb-8 pb-8 border-b">
        <Tag className="w-5 h-5 text-gray-400" />
        <div className="flex flex-wrap gap-2">
          {post.tags.map((tag) => (
            <span
              key={tag}
              className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-full text-sm transition-colors cursor-pointer"
            >
              {tag}
            </span>
          ))}
        </div>
      </div>

      {/* Related Posts */}
      {relatedPosts.length > 0 && (
        <section className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Related Posts</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {relatedPosts.map((relatedPost) => (
              <Link
                key={relatedPost.id}
                to={`/blog/${relatedPost.slug}`}
                className="bg-white border border-gray-200 rounded-lg overflow-hidden hover:shadow-lg transition-shadow"
              >
                <img
                  src={relatedPost.coverImage}
                  alt={relatedPost.title}
                  className="w-full h-32 object-cover"
                />
                <div className="p-4">
                  <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">
                    {relatedPost.title}
                  </h3>
                  <p className="text-sm text-gray-600 line-clamp-2">
                    {relatedPost.excerpt}
                  </p>
                </div>
              </Link>
            ))}
          </div>
        </section>
      )}

      {/* Navigation */}
      <div className="flex justify-between items-center pt-8 border-t">
        <Link
          to="/blog"
          className="flex items-center space-x-2 text-blue-600 hover:text-blue-700 transition-colors"
        >
          <ArrowLeft className="w-5 h-5" />
          <span>Back to Blog</span>
        </Link>
        
        <div className="text-right">
          <p className="text-sm text-gray-500">Share this article</p>
          <div className="flex space-x-2 mt-1">
            <button
              onClick={() => handleShare('twitter')}
              className="text-blue-400 hover:text-blue-500 transition-colors"
            >
              Twitter
            </button>
            <button
              onClick={() => handleShare('facebook')}
              className="text-blue-600 hover:text-blue-700 transition-colors"
            >
              Facebook
            </button>
            <button
              onClick={() => handleShare('linkedin')}
              className="text-blue-800 hover:text-blue-900 transition-colors"
            >
              LinkedIn
            </button>
          </div>
        </div>
      </div>
    </article>
  );
};

export default BlogPage;