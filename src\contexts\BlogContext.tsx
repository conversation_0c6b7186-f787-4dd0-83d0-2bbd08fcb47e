import React, { createContext, useContext, useState, useEffect } from 'react';
import { BlogPost, Category, AnalyticsData } from '../types';
import apiService from '../services/api';

interface BlogContextType {
  posts: BlogPost[];
  categories: Category[];
  analytics: AnalyticsData | null;
  isLoading: boolean;
  error: string | null;
  // Posts
  fetchPosts: (params?: any) => Promise<void>;
  fetchPostBySlug: (slug: string) => Promise<BlogPost | null>;
  fetchFeaturedPosts: () => Promise<void>;
  createPost: (post: any) => Promise<void>;
  updatePost: (id: string, post: any) => Promise<void>;
  deletePost: (id: string) => Promise<void>;
  likePost: (id: string) => Promise<void>;
  // Categories
  fetchCategories: () => Promise<void>;
  fetchCategoryBySlug: (slug: string) => Promise<Category | null>;
  // Analytics
  fetchAnalytics: () => Promise<void>;
  trackPageView: (page: string) => Promise<void>;
  // Legacy methods for backward compatibility
  getPostBySlug: (slug: string) => BlogPost | undefined;
  getPostsByCategory: (category: string) => BlogPost[];
  getFeaturedPosts: () => BlogPost[];
}

const BlogContext = createContext<BlogContextType | undefined>(undefined);

export const useBlog = () => {
  const context = useContext(BlogContext);
  if (!context) {
    throw new Error('useBlog must be used within BlogProvider');
  }
  return context;
};



export const BlogProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [analytics, setAnalytics] = useState<AnalyticsData | null>({
    pageViews: 45231,
    uniqueVisitors: 12543,
    averageSessionDuration: 204,
    bounceRate: 0.342,
    topPages: [
      { page: '/best-electric-scooters-2024', views: 3245 },
      { page: '/scooter-safety-tips', views: 2876 },
      { page: '/electric-vs-gas-scooters', views: 2543 }
    ],
    trafficSources: [
      { source: 'Organic Search', visitors: 8234 },
      { source: 'Direct', visitors: 2156 },
      { source: 'Social Media', visitors: 1543 }
    ],
    recentActivity: [
      { visitor: 'New York', page: '/best-electric-scooters-2024', timestamp: '2 minutes ago' },
      { visitor: 'California', page: '/scooter-safety-tips', timestamp: '5 minutes ago' }
    ]
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Initialize with default data
  useEffect(() => {
    const initializeData = () => {
      // Default posts
      const defaultPosts: BlogPost[] = [
        {
          id: '1',
          title: 'Best Electric Scooters for 2024: Complete Buyer\'s Guide',
          slug: 'best-electric-scooters-2024-buyers-guide',
          excerpt: 'Discover the top electric scooters of 2024 with our comprehensive buyer\'s guide. We\'ve tested and reviewed the latest models to help you make the perfect choice.',
          content: 'Full content here...',
          coverImage: 'https://images.pexels.com/photos/3799821/pexels-photo-3799821.jpeg?auto=compress&cs=tinysrgb&w=800',
          author: 'John Doe',
          category: 'Reviews',
          tags: ['electric scooters', 'reviews', '2024', 'buying guide'],
          featured: true,
          status: 'published' as const,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-15T10:00:00Z',
          readTime: 8,
          views: 1250,
          likes: 45,
          affiliateLinks: []
        },
        {
          id: '2',
          title: 'Electric Scooter Safety Tips for Beginners',
          slug: 'electric-scooter-safety-tips-beginners',
          excerpt: 'Essential safety guidelines every new electric scooter rider should know before hitting the road.',
          content: 'Full content here...',
          coverImage: 'https://images.pexels.com/photos/7163619/pexels-photo-7163619.jpeg?auto=compress&cs=tinysrgb&w=800',
          author: 'Jane Smith',
          category: 'Safety',
          tags: ['safety', 'beginners', 'tips'],
          featured: true,
          status: 'published' as const,
          createdAt: '2024-01-14T10:00:00Z',
          updatedAt: '2024-01-14T10:00:00Z',
          readTime: 5,
          views: 890,
          likes: 32,
          affiliateLinks: []
        },
        {
          id: '3',
          title: 'Electric vs Gas Scooters: Complete Comparison',
          slug: 'electric-vs-gas-scooters-comparison',
          excerpt: 'A comprehensive analysis of electric and gas-powered scooters to help you make the right choice.',
          content: 'Full content here...',
          coverImage: 'https://images.pexels.com/photos/1416736/pexels-photo-1416736.jpeg?auto=compress&cs=tinysrgb&w=800',
          author: 'Mike Johnson',
          category: 'Comparison',
          tags: ['electric', 'gas', 'comparison'],
          featured: true,
          status: 'published' as const,
          createdAt: '2024-01-13T10:00:00Z',
          updatedAt: '2024-01-13T10:00:00Z',
          readTime: 10,
          views: 756,
          likes: 28,
          affiliateLinks: []
        }
      ];

      // Default categories
      const defaultCategories: Category[] = [
        {
          _id: '1',
          name: 'Reviews',
          slug: 'reviews',
          description: 'In-depth reviews of electric scooters',
          postCount: 15
        },
        {
          _id: '2',
          name: 'Safety',
          slug: 'safety',
          description: 'Safety tips and guidelines',
          postCount: 8
        },
        {
          _id: '3',
          name: 'Comparison',
          slug: 'comparison',
          description: 'Product comparisons and analysis',
          postCount: 12
        }
      ];

      setPosts(defaultPosts);
      setCategories(defaultCategories);
    };

    initializeData();
  }, []);

  // Fetch posts with optional parameters
  const fetchPosts = async (params: any = {}) => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getPosts(params);
      if (response.success && response.data) {
        setPosts(response.data.posts);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch posts');
      console.error('Error fetching posts:', err);
      // Keep existing posts if fetch fails
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch single post by slug
  const fetchPostBySlug = async (slug: string): Promise<BlogPost | null> => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getPostBySlug(slug);
      if (response.success && response.data) {
        // Track page view
        await trackPageView(`/blog/${slug}`);
        return response.data.post;
      }
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch post');
      console.error('Error fetching post:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch featured posts
  const fetchFeaturedPosts = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getFeaturedPosts();
      if (response.success && response.data) {
        setPosts(response.data.posts);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch featured posts');
      console.error('Error fetching featured posts:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Create new post
  const createPost = async (postData: any) => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.createPost(postData);
      if (response.success && response.data) {
        setPosts(prev => [response.data!.post, ...prev]);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create post');
      console.error('Error creating post:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Update post
  const updatePost = async (id: string, postData: any) => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.updatePost(id, postData);
      if (response.success && response.data) {
        setPosts(prev => prev.map(post =>
          post.id === id ? response.data!.post : post
        ));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update post');
      console.error('Error updating post:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Delete post
  const deletePost = async (id: string) => {
    try {
      setIsLoading(true);
      setError(null);
      await apiService.deletePost(id);
      setPosts(prev => prev.filter(post => post.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete post');
      console.error('Error deleting post:', err);
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  // Like post
  const likePost = async (id: string) => {
    try {
      const response = await apiService.likePost(id);
      if (response.success && response.data) {
        setPosts(prev => prev.map(post =>
          post.id === id ? { ...post, likes: response.data!.likes } : post
        ));
      }
    } catch (err) {
      console.error('Error liking post:', err);
    }
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getCategories();
      if (response.success && response.data) {
        setCategories(response.data.categories);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch categories');
      console.error('Error fetching categories:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch category by slug
  const fetchCategoryBySlug = async (slug: string): Promise<Category | null> => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getCategoryBySlug(slug);
      if (response.success && response.data) {
        return response.data.category;
      }
      return null;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch category');
      console.error('Error fetching category:', err);
      return null;
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch analytics
  const fetchAnalytics = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const response = await apiService.getAnalytics();
      if (response.success && response.data) {
        setAnalytics(response.data.analytics);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch analytics');
      console.error('Error fetching analytics:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Track page view
  const trackPageView = async (page: string) => {
    try {
      await apiService.trackPageView(page, navigator.userAgent, document.referrer);
    } catch (err) {
      console.error('Error tracking page view:', err);
    }
  };

  // Legacy methods for backward compatibility
  const getPostBySlug = (slug: string) => {
    return posts.find(post => post.slug === slug);
  };

  const getPostsByCategory = (category: string) => {
    return posts.filter(post => post.category === category);
  };

  const getFeaturedPosts = () => {
    return posts.filter(post => post.featured);
  };

  // Load initial data
  useEffect(() => {
    fetchCategories();
    fetchPosts({ status: 'published' });
  }, []);

  return (
    <BlogContext.Provider value={{
      posts,
      categories,
      analytics,
      isLoading,
      error,
      // API methods
      fetchPosts,
      fetchPostBySlug,
      fetchFeaturedPosts,
      createPost,
      updatePost,
      deletePost,
      likePost,
      fetchCategories,
      fetchCategoryBySlug,
      fetchAnalytics,
      trackPageView,
      // Legacy methods
      getPostBySlug,
      getPostsByCategory,
      getFeaturedPosts
    }}>
      {children}
    </BlogContext.Provider>
  );
};