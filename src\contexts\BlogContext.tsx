import React, { createContext, useContext, useState, useEffect } from 'react';
import { BlogPost, Category, AnalyticsData } from '../types';

interface BlogContextType {
  posts: BlogPost[];
  categories: Category[];
  analytics: AnalyticsData;
  isLoading: boolean;
  createPost: (post: Omit<BlogPost, 'id'>) => void;
  updatePost: (id: string, post: Partial<BlogPost>) => void;
  deletePost: (id: string) => void;
  getPostBySlug: (slug: string) => BlogPost | undefined;
  getPostsByCategory: (category: string) => BlogPost[];
  getFeaturedPosts: () => BlogPost[];
}

const BlogContext = createContext<BlogContextType | undefined>(undefined);

export const useBlog = () => {
  const context = useContext(BlogContext);
  if (!context) {
    throw new Error('useBlog must be used within BlogProvider');
  }
  return context;
};

const mockPosts: BlogPost[] = [
  {
    id: '1',
    title: 'Xiaomi Mi Electric Scooter Pro 2 Review: The Perfect Urban Commuter',
    slug: 'xiaomi-mi-electric-scooter-pro-2-review',
    excerpt: 'An in-depth review of the Xiaomi Mi Electric Scooter Pro 2, covering performance, battery life, and value for money.',
    content: `# Xiaomi Mi Electric Scooter Pro 2 Review

The Xiaomi Mi Electric Scooter Pro 2 has been making waves in the electric scooter market, and for good reason. After extensive testing, here's our comprehensive review.

## Build Quality and Design

The Pro 2 features a sleek, minimalist design that's become synonymous with Xiaomi products. The aluminum alloy frame feels solid and well-built, while the folding mechanism is both sturdy and convenient.

## Performance

- **Top Speed**: 25 km/h (15.5 mph)
- **Range**: Up to 45km on a single charge
- **Motor Power**: 300W nominal, 600W peak
- **Max Load**: 100kg

## Battery Life

The 474Wh battery provides excellent range, easily handling daily commutes. Charging takes about 8 hours from empty to full.

## Verdict

The Xiaomi Mi Electric Scooter Pro 2 offers excellent value for money with reliable performance and build quality. Highly recommended for urban commuters.

**Rating: 4.5/5 stars**`,
    coverImage: 'https://images.pexels.com/photos/3799821/pexels-photo-3799821.jpeg?auto=compress&cs=tinysrgb&w=800',
    category: 'Reviews',
    tags: ['Xiaomi', 'Electric Scooter', 'Urban Mobility', 'Commuting'],
    author: 'Alex Johnson',
    publishedAt: '2024-01-15',
    readTime: 8,
    featured: true,
    seoTitle: 'Xiaomi Mi Electric Scooter Pro 2 Review - Complete Guide 2024',
    seoDescription: 'Complete review of the Xiaomi Mi Electric Scooter Pro 2. Performance, battery life, build quality and more. Is it worth buying in 2024?',
    affiliateLinks: [
      {
        id: '1',
        text: 'Buy on Amazon',
        url: 'https://amazon.com/xiaomi-electric-scooter-pro-2',
        productName: 'Xiaomi Mi Electric Scooter Pro 2',
        price: '$599',
        discount: '15% off'
      }
    ]
  },
  {
    id: '2',
    title: 'Top 10 Electric Scooters for 2024: Complete Buyer\'s Guide',
    slug: 'top-10-electric-scooters-2024-buyers-guide',
    excerpt: 'Discover the best electric scooters of 2024 with our comprehensive buyer\'s guide featuring detailed comparisons and expert recommendations.',
    content: `# Top 10 Electric Scooters for 2024

Finding the perfect electric scooter can be overwhelming with so many options available. We've tested dozens of models to bring you this definitive guide.

## Our Testing Methodology

We evaluate each scooter based on:
- Performance and speed
- Battery life and range
- Build quality and durability
- Value for money
- User experience

## Top 10 List

### 1. Xiaomi Mi Electric Scooter Pro 2
The gold standard for urban commuting with excellent range and reliability.

### 2. Segway Ninebot MAX G30
Robust build quality with impressive 65km range makes this perfect for longer commutes.

### 3. Apollo City Pro
Premium features including dual suspension and powerful motor for comfortable rides.

[Continue with remaining 7 scooters...]

## Buying Guide

Consider these factors when choosing your electric scooter:
- **Daily Range**: How far do you need to travel?
- **Terrain**: Flat city streets or hilly areas?
- **Weight**: Do you need to carry it upstairs?
- **Budget**: Electric scooters range from $300 to $3000+

## Conclusion

The best electric scooter depends on your specific needs and budget. For most urban commuters, we recommend starting with the Xiaomi Mi Electric Scooter Pro 2 for its excellent balance of features and value.`,
    coverImage: 'https://images.pexels.com/photos/4254555/pexels-photo-4254555.jpeg?auto=compress&cs=tinysrgb&w=800',
    category: 'Buying Guides',
    tags: ['Best Of', 'Buying Guide', '2024', 'Comparison'],
    author: 'Sarah Chen',
    publishedAt: '2024-01-10',
    readTime: 12,
    featured: true,
    affiliateLinks: []
  },
  {
    id: '3',
    title: 'Electric Scooter Safety Tips: Riding Responsibly in the City',
    slug: 'electric-scooter-safety-tips-city-riding',
    excerpt: 'Essential safety tips for electric scooter riders, including protective gear, traffic rules, and defensive riding techniques.',
    content: `# Electric Scooter Safety: Your Complete Guide

Safety should always be your top priority when riding an electric scooter. Here's everything you need to know to ride safely and responsibly.

## Essential Safety Gear

### Helmet
Always wear a properly fitted helmet. Look for:
- CPSC or SNELL certification
- Proper fit (snug but comfortable)
- Good ventilation
- Bright colors for visibility

### Protective Clothing
- Long pants to protect legs
- Closed-toe shoes with good grip
- Reflective clothing for night riding
- Knee and elbow pads for beginners

## Traffic Rules and Regulations

### Know Your Local Laws
- Speed limits (typically 15-25 mph)
- Where you can ride (bike lanes, roads, sidewalks)
- Age restrictions
- Registration requirements

### Follow Traffic Rules
- Obey traffic signals and signs
- Ride in the direction of traffic
- Use hand signals for turns
- Yield to pedestrians

## Defensive Riding Techniques

### Visibility
- Use lights and reflectors
- Wear bright clothing
- Make eye contact with drivers
- Avoid blind spots

### Speed Management
- Ride at safe speeds for conditions
- Slow down in crowded areas
- Watch for road hazards
- Leave stopping distance

## Maintenance for Safety

Regular maintenance ensures your scooter operates safely:
- Check brakes before each ride
- Inspect tires for wear and proper pressure
- Ensure all bolts are tight
- Keep battery charged and in good condition

## Emergency Preparedness

- Carry a first aid kit
- Know basic repair skills
- Have emergency contacts readily available
- Consider scooter insurance

## Conclusion

Electric scooter safety is about being prepared, following rules, and riding defensively. With proper precautions, you can enjoy the convenience and fun of electric scooter riding while staying safe.`,
    coverImage: 'https://images.pexels.com/photos/6480688/pexels-photo-6480688.jpeg?auto=compress&cs=tinysrgb&w=800',
    category: 'Safety',
    tags: ['Safety', 'Tips', 'City Riding', 'Regulations'],
    author: 'Mike Rodriguez',
    publishedAt: '2024-01-05',
    readTime: 6,
    featured: false,
    affiliateLinks: [
      {
        id: '2',
        text: 'Best Helmet on Amazon',
        url: 'https://amazon.com/electric-scooter-helmet',
        productName: 'Premium Electric Scooter Helmet',
        price: '$49',
        discount: '20% off'
      }
    ]
  }
];

const mockCategories: Category[] = [
  { id: '1', name: 'Reviews', slug: 'reviews', description: 'In-depth electric scooter reviews', postCount: 15 },
  { id: '2', name: 'Buying Guides', slug: 'buying-guides', description: 'Help choosing the right scooter', postCount: 8 },
  { id: '3', name: 'Safety', slug: 'safety', description: 'Safety tips and regulations', postCount: 5 },
  { id: '4', name: 'Maintenance', slug: 'maintenance', description: 'Keep your scooter running smoothly', postCount: 12 },
  { id: '5', name: 'News', slug: 'news', description: 'Latest electric scooter industry news', postCount: 20 }
];

const mockAnalytics: AnalyticsData = {
  pageViews: 45230,
  uniqueVisitors: 32180,
  averageSessionDuration: 245,
  bounceRate: 0.34,
  topPages: [
    { page: '/blog/xiaomi-mi-electric-scooter-pro-2-review', views: 8420 },
    { page: '/blog/top-10-electric-scooters-2024-buyers-guide', views: 6830 },
    { page: '/', views: 5240 },
    { page: '/blog/electric-scooter-safety-tips-city-riding', views: 3920 },
    { page: '/categories/reviews', views: 2810 }
  ],
  trafficSources: [
    { source: 'Google Search', visitors: 18240 },
    { source: 'Direct', visitors: 8920 },
    { source: 'Social Media', visitors: 3420 },
    { source: 'Referral', visitors: 1600 }
  ],
  recentActivity: [
    { timestamp: '2024-01-20 14:30', page: '/blog/xiaomi-mi-electric-scooter-pro-2-review', visitor: '***********' },
    { timestamp: '2024-01-20 14:28', page: '/categories/reviews', visitor: '***********' },
    { timestamp: '2024-01-20 14:25', page: '/', visitor: '************' }
  ]
};

export const BlogProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [posts, setPosts] = useState<BlogPost[]>(mockPosts);
  const [categories] = useState<Category[]>(mockCategories);
  const [analytics] = useState<AnalyticsData>(mockAnalytics);
  const [isLoading, setIsLoading] = useState(false);

  const createPost = (newPost: Omit<BlogPost, 'id'>) => {
    const post: BlogPost = {
      ...newPost,
      id: Date.now().toString(),
    };
    setPosts(prev => [post, ...prev]);
  };

  const updatePost = (id: string, updatedPost: Partial<BlogPost>) => {
    setPosts(prev => prev.map(post => 
      post.id === id ? { ...post, ...updatedPost } : post
    ));
  };

  const deletePost = (id: string) => {
    setPosts(prev => prev.filter(post => post.id !== id));
  };

  const getPostBySlug = (slug: string) => {
    return posts.find(post => post.slug === slug);
  };

  const getPostsByCategory = (category: string) => {
    return posts.filter(post => post.category === category);
  };

  const getFeaturedPosts = () => {
    return posts.filter(post => post.featured);
  };

  return (
    <BlogContext.Provider value={{
      posts,
      categories,
      analytics,
      isLoading,
      createPost,
      updatePost,
      deletePost,
      getPostBySlug,
      getPostsByCategory,
      getFeaturedPosts
    }}>
      {children}
    </BlogContext.Provider>
  );
};