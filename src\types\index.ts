export interface BlogPost {
  id: string;
  title: string;
  slug: string;
  excerpt: string;
  content: string;
  coverImage: string;
  category: string;
  tags: string[];
  author: string;
  publishedAt: string;
  readTime: number;
  featured: boolean;
  seoTitle?: string;
  seoDescription?: string;
  affiliateLinks: AffiliateLink[];
}

export interface AffiliateLink {
  id: string;
  text: string;
  url: string;
  productName: string;
  price?: string;
  discount?: string;
}

export interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  postCount: number;
}

export interface AnalyticsData {
  pageViews: number;
  uniqueVisitors: number;
  averageSessionDuration: number;
  bounceRate: number;
  topPages: { page: string; views: number }[];
  trafficSources: { source: string; visitors: number }[];
  recentActivity: { timestamp: string; page: string; visitor: string }[];
}

export interface User {
  id: string;
  name: string;
  email: string;
  role: 'admin' | 'editor';
  avatar?: string;
}